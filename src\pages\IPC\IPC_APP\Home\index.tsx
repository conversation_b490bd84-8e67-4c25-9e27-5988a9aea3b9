import { Image, Grid, List, Button } from "antd-mobile";
import { useHistory, useRouteMatch } from "react-router-dom";
import { useRequest } from "ahooks";
import { getVideoRecord } from "@/api/ipc";
import { exitWebClient } from "@/api/cameraPlayer";

import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import tip from "@/Resources/camMgmtImg/tip.png";
import plus from "@/Resources/camMgmtImg/plus.png";
import enterRight from "@/Resources/camMgmtImg/enter-right.png";
import ai from "@/Resources/camMgmtImg/ai.png";
import device from "@/Resources/camMgmtImg/device.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import tipDark from "@/Resources/camMgmtImg/tip-dark.png";
import plusDark from "@/Resources/camMgmtImg/plus-dark.png";
import enterRightDark from "@/Resources/camMgmtImg/enter-right-dark.png";
import aiDark from "@/Resources/camMgmtImg/ai-dark.png";
import deviceDark from "@/Resources/camMgmtImg/device-dark.png";
import plays from "@/Resources/camMgmtImg/plays.png";
import deviceOffline from "@/Resources/camMgmtImg/device-offline.png";
import emptyCamera from "@/Resources/camMgmtImg/empty-camera.png";
import manager_error_img from "@/Resources/camera_poster/camera_manager_poster_pc.png";
import manager_error_img_dark from "@/Resources/camera_poster/camera_manager_poster_pc_dark.png";
import event_error_img_dark from "@/Resources/camera_poster/camera_manager_eventLookback_poster_dark.png";
import event_error_img from "@/Resources/camera_poster/camera_manager_eventLookback_poster.png";
import { useTheme } from "@/utils/themeDetector";
import { useCallback, useEffect, useMemo } from "react";
import { CameraInfo } from "@/api/ipc";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import { PreloadImage } from "@/components/Image";

// 模拟事件回看数据 - 作为默认数据
interface IPCHomeProps {
  cameraList: CameraInfo[];
  refreshCameraList: () => void;
  isLoading?: boolean;
}

const IPCHome: React.FC<IPCHomeProps> = ({ cameraList, refreshCameraList, isLoading = false }) => {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const { path } = useRouteMatch();

  // 获取事件回看数据
  const { data: rawEventData, loading: eventDataLoading } = useRequest(() =>
    getVideoRecord({
      page: {
        size: 20,
        token: "",
      },
      options: {
        option: ["event_name"],
        event_name: ["human", "fire", "pet", "move"],
      },
    },
      { showLoading: false })
  );

  // 处理事件回看数据
  const eventReplayData = useMemo(() => {
    if (rawEventData && rawEventData.code === 0 && rawEventData.data?.videos) {
      // 处理视频数据，只取前三条
      return rawEventData.data.videos
        .slice(0, 3)
        .map((video: any, index: number) => ({
          id: index + 1,
          src: video.cover_file ? `${video.cover_file}/original.jpg` : "",
          time: new Date(video.time).toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          }),
        }));
    }
    return [];
  }, [rawEventData]);

  const cameraOnClick = useCallback(
    (camera: CameraInfo) => {
      // 改为对象形式传递，防止刷新丢失数据问题
      history.push({
        pathname: `${path}/cameraDetail`,
        state: { cameraDetail: camera },
      });
    },
    [history, path]
  );

  // 处理返回按钮点击
  const handleBackClick = () => {
    exitWebClient().catch((error) => {
      console.error("退出Web客户端失败:", error);
    });
  };

  // 初始化时记录摄像机管理页面曝光一次
  useEffect(() => {
    window.onetrack?.('track', 'ipc_cameraManager_expose');
  }, [])

  // 摄像机骨架屏组件
  const CameraSkeleton = () => (
    <div className={styles.skeletonContainer}>
      <div className={styles.cameraSkeletonGrid}>
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className={styles.cameraSkeletonItem} />
        ))}
      </div>
    </div>
  );

  // 事件回看骨架屏组件
  const EventSkeleton = () => (
    <div className={styles.eventSkeletonContainer}>
      <div className={styles.eventSkeletonHeader}>
        <div className={styles.eventSkeletonTitle} />
        <div className={styles.eventSkeletonViewAll} />
      </div>
      <div className={styles.eventSkeletonImages}>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className={styles.eventSkeletonImageItem}>
            <div className={styles.eventSkeletonTime} />
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={styles.cameraManagementContainer}>
      <div className={styles.header}>
        <Image
          className={styles.backIcon}
          src={isDarkMode ? arrowLeftDark : arrowLeft}
          onClick={handleBackClick}
        />
        <div className={styles.iconContainer}>
          <Image
            className={styles.infoIcon}
            src={isDarkMode ? tipDark : tip}
            onClick={() => history.push(`${path}/supportInformation`)}
          />
          <Image
            className={styles.addIcon}
            src={isDarkMode ? plusDark : plus}
            onClick={() => history.push(`${path}/addDevice`)}
          />
        </div>
      </div>

      <div className={styles.title}>摄像机管理</div>

      <div className={styles.scrollContainer}>
        {!isLoading ? (
          <CameraSkeleton />
        ) : (
          <Grid columns={2} style={{ marginTop: 10 }}>
            {cameraList.map((camera) => {
              // 取主摄镜头
              const mainLens = camera.key_frame.find((k) => k.lens_id === 0);
              let imgSrc = mainLens?.frame || "";
              if (!camera.isOnline) {
                imgSrc = deviceOffline;
              } else if (mainLens?.psm) {
                imgSrc = emptyCamera;
              }
              return (
                <Grid.Item key={camera.did}>
                  <div
                    className={styles.cameraItem}
                    onClick={() => cameraOnClick(camera)}
                  >
                    <PreloadImage
                      className={styles.cameraImage}
                      src={splitURL(imgSrc)}
                      style={{ width: "100%", height: "100%" }}
                      needHeader={true}
                      errorImage={isDarkMode ? manager_error_img_dark : manager_error_img}
                      onError={() => {
                        console.log('摄像机图片加载失败');
                      }}
                    />
                  </div>
                </Grid.Item>
              );
            })}
          </Grid>
        )}

        {eventDataLoading ? (
          <EventSkeleton />
        ) : (
          Array.isArray(eventReplayData) && eventReplayData.length > 0 && (
            <div className={styles.eventReplayContainer}>
              <List.Item className={styles.eventReplayItem} arrow="">
                <div className={styles.all}>
                  <div className={styles.eventReplayTitle}>事件回看</div>
                  <div className={styles.viewAll}>
                    <div
                      className={styles.view}
                      onClick={() =>
                        history.push(`${path}/cameraDetail/eventLookBack`)
                      }
                    >
                      查看全部
                    </div>
                    <Image
                      className={styles.viewIcon}
                      src={isDarkMode ? enterRightDark : enterRight}
                    />
                  </div>
                </div>
                <div className={styles.eventReplayImages}>
                  {eventReplayData.map((event: any) => {
                    return (
                      <div key={event.id} className={styles.eventReplayImageItem}>
                        <PreloadImage
                          className={styles.eventReplayImage}
                          src={splitURL(event.src)}
                          needHeader={true}
                          style={{ width: "100%", height: "100%" }}
                          errorImage={isDarkMode ? event_error_img_dark : event_error_img}
                        />
                        <div className={styles.eventReplayTime}>
                          <Image src={plays} className={styles.playIcon} />
                          {event.time}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </List.Item>
            </div>
          )
        )}

        <div className={styles.storageManagementContainer}>
          <Button
            className={styles.storageManagementButton}
            onClick={() => history.push(`${path}/videoList`)}
          >
            <img
              src={isDarkMode ? deviceDark : device}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>存储管理</span>
          </Button>
        </div>

        <div className={styles.functionButtonsContainer}>
          <Button
            className={styles.aiButton}
            onClick={() => history.push(`${path}/faceRecognition`)}
          >
            <img
              src={isDarkMode ? aiDark : ai}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>AI功能</span>
          </Button>
          <Button
            className={styles.deviceButton}
            onClick={() =>
              history.push({
                pathname: `${path}/storageManagement`,
                state: {
                  cameraList,
                },
              })
            }
          >
            <img
              src={isDarkMode ? deviceDark : device}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>设备管理</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default IPCHome;
